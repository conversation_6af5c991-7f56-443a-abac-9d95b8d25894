import ContactForm from '../components/Contact/ContactForm';
import { Phone, Mail, Clock, MapPin, MessageCircle } from 'lucide-react';

const Contact = () => {
  const contactMethods = [
    {
      icon: Phone,
      title: 'Call Us',
      details: '************',
      description: 'Speak directly with our team',
      action: 'tel:+27833467187'
    },
    {
      icon: Mail,
      title: 'Email Us',
      details: '<EMAIL>',
      description: 'Send us your project details',
      action: 'mailto:<EMAIL>'
    },
    {
      icon: MessageCircle,
      title: 'WhatsApp',
      details: 'Quick Response',
      description: 'Get instant support',
      action: 'https://wa.me/27833467187'
    }
  ];

  const serviceAreas = [
    'Gauteng',
    'Cape Town',
    'Durban',
    'Polokwane',
    'Kimberley',
    'Bloemfontein',
    'Nelspruit'
  ];

  return (
    <div>
      {/* Page Header */}
      <section className="bg-gradient-to-r from-primary-600 to-primary-700 text-white py-16">
        <div className="container-custom">
          <div className="text-center">
            <h1 className="heading-xl mb-4">Contact Us</h1>
            <p className="text-xl text-primary-100 max-w-3xl mx-auto">
              Ready to start your project? Get in touch with our expert team for a free 
              consultation and detailed quote. We're here to bring your vision to life.
            </p>
          </div>
        </div>
      </section>

      {/* Contact Methods */}
      <section className="section-padding bg-gray-50">
        <div className="container-custom">
          <div className="text-center mb-12">
            <h2 className="heading-lg text-secondary-900 mb-4">
              Get In Touch
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Choose the most convenient way to reach us. We're committed to responding 
              to all inquiries within 24 hours.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            {contactMethods.map((method, index) => {
              const IconComponent = method.icon;
              return (
                <a
                  key={index}
                  href={method.action}
                  className="bg-white rounded-xl p-8 shadow-sm hover:shadow-md transition-shadow duration-300 text-center group"
                >
                  <div className="bg-primary-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4 group-hover:bg-primary-200 transition-colors duration-300">
                    <IconComponent className="h-8 w-8 text-primary-600" />
                  </div>
                  <h3 className="text-xl font-semibold text-secondary-900 mb-2">
                    {method.title}
                  </h3>
                  <p className="text-primary-600 font-medium mb-2">
                    {method.details}
                  </p>
                  <p className="text-gray-600 text-sm">
                    {method.description}
                  </p>
                </a>
              );
            })}
          </div>

          {/* Business Hours & Location */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="bg-white rounded-xl p-8 shadow-sm">
              <div className="flex items-center space-x-3 mb-6">
                <div className="bg-primary-100 rounded-lg p-3">
                  <Clock className="h-6 w-6 text-primary-600" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-secondary-900">
                    Business Hours
                  </h3>
                  <p className="text-gray-600">When we're available</p>
                </div>
              </div>
              
              <div className="space-y-3">
                <div className="flex justify-between items-center py-2 border-b border-gray-100">
                  <span className="font-medium text-gray-700">Monday - Friday</span>
                  <span className="text-primary-600 font-medium">7:00 AM - 5:00 PM</span>
                </div>
                <div className="flex justify-between items-center py-2 border-b border-gray-100">
                  <span className="font-medium text-gray-700">Saturday</span>
                  <span className="text-gray-500">Closed</span>
                </div>
                <div className="flex justify-between items-center py-2">
                  <span className="font-medium text-gray-700">Sunday</span>
                  <span className="text-gray-500">Emergency Only</span>
                </div>
              </div>

              <div className="mt-6 p-4 bg-primary-50 rounded-lg">
                <p className="text-sm text-primary-700">
                  <strong>Emergency Services:</strong> Available for urgent repairs and maintenance issues.
                </p>
              </div>
            </div>

            <div className="bg-white rounded-xl p-8 shadow-sm">
              <div className="flex items-center space-x-3 mb-6">
                <div className="bg-primary-100 rounded-lg p-3">
                  <MapPin className="h-6 w-6 text-primary-600" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-secondary-900">
                    Service Areas
                  </h3>
                  <p className="text-gray-600">Where we operate</p>
                </div>
              </div>
              
              <p className="text-gray-600 mb-4">
                Based in Gauteng, we can work in other provinces when needed:
              </p>

              <div className="grid grid-cols-2 gap-3">
                {serviceAreas.map((area, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-primary-600 rounded-full"></div>
                    <span className="text-gray-700">{area}</span>
                  </div>
                ))}
              </div>

              <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                <p className="text-sm text-gray-600">
                  <strong>Note:</strong> Travel costs may apply for projects outside our primary service areas. 
                  Contact us for details.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="section-padding bg-white">
        <div className="container-custom">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-start">
            {/* Form */}
            <ContactForm />

            {/* Additional Information */}
            <div className="space-y-8">
              <div>
                <h3 className="text-2xl font-semibold text-secondary-900 mb-4">
                  Why Choose Arebone?
                </h3>
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <div className="bg-primary-100 rounded-full p-2 mt-1">
                      <div className="w-2 h-2 bg-primary-600 rounded-full"></div>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">12+ Years Experience</h4>
                      <p className="text-gray-600 text-sm">Proven track record in construction and building services</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3">
                    <div className="bg-primary-100 rounded-full p-2 mt-1">
                      <div className="w-2 h-2 bg-primary-600 rounded-full"></div>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">Certified Professionals</h4>
                      <p className="text-gray-600 text-sm">Belgotex preferred installer and Plascon certification pending</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3">
                    <div className="bg-primary-100 rounded-full p-2 mt-1">
                      <div className="w-2 h-2 bg-primary-600 rounded-full"></div>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">Quality Guaranteed</h4>
                      <p className="text-gray-600 text-sm">All work backed by our commitment to excellence</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3">
                    <div className="bg-primary-100 rounded-full p-2 mt-1">
                      <div className="w-2 h-2 bg-primary-600 rounded-full"></div>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">Provincial Coverage</h4>
                      <p className="text-gray-600 text-sm">Based in Gauteng, available for projects in other provinces</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-xl p-6 text-white">
                <h4 className="text-lg font-semibold mb-3">Need Immediate Assistance?</h4>
                <p className="text-primary-100 mb-4 text-sm">
                  For urgent matters or emergency repairs, don't hesitate to call us directly.
                </p>
                <a
                  href="tel:+27833467187"
                  className="bg-white text-primary-600 hover:bg-gray-50 font-medium py-2 px-4 rounded-lg text-sm transition-colors duration-200 inline-block"
                >
                  Call Now: ************
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Contact;

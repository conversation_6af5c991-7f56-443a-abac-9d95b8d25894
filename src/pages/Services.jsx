import ServiceDetails from '../components/Services/ServiceDetails';
import { Phone, Mail, Clock } from 'lucide-react';

const Services = () => {
  return (
    <div>
      {/* Page Header */}
      <section className="bg-gradient-to-r from-primary-600 to-primary-700 text-white py-16">
        <div className="container-custom">
          <div className="text-center">
            <h1 className="heading-xl mb-4">Our Services</h1>
            <p className="text-xl text-primary-100 max-w-3xl mx-auto">
              Comprehensive construction and building services delivered with professional 
              excellence and quality assurance across South Africa.
            </p>
          </div>
        </div>
      </section>

      {/* Service Overview */}
      <section className="section-padding bg-gray-50">
        <div className="container-custom">
          <div className="text-center mb-12">
            <h2 className="heading-lg text-secondary-900 mb-4">
              Professional Construction Solutions
            </h2>
            <p className="text-lg text-gray-600 max-w-4xl mx-auto">
              From new construction to specialized maintenance, we deliver comprehensive 
              building services backed by industry certifications and years of experience.
            </p>
          </div>

          {/* Service Categories */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-200 text-center">
              <div className="bg-primary-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🏗️</span>
              </div>
              <h3 className="text-xl font-semibold text-secondary-900 mb-2">
                Construction & Building
              </h3>
              <p className="text-gray-600 text-sm">
                Complete construction solutions from foundation to finish
              </p>
            </div>

            <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-200 text-center">
              <div className="bg-primary-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🔧</span>
              </div>
              <h3 className="text-xl font-semibold text-secondary-900 mb-2">
                Maintenance & Repairs
              </h3>
              <p className="text-gray-600 text-sm">
                Professional maintenance to keep your property in excellent condition
              </p>
            </div>

            <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-200 text-center">
              <div className="bg-primary-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🎨</span>
              </div>
              <h3 className="text-xl font-semibold text-secondary-900 mb-2">
                Specialized Services
              </h3>
              <p className="text-gray-600 text-sm">
                Expert flooring, painting, and custom installation services
              </p>
            </div>
          </div>
        </div>
      </section>

      <ServiceDetails />

      {/* Contact CTA */}
      <section className="section-padding bg-secondary-800 text-white">
        <div className="container-custom">
          <div className="text-center mb-12">
            <h2 className="heading-lg mb-4">Ready to Start Your Project?</h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Contact our expert team today for a free consultation and detailed quote. 
              We're here to bring your vision to life with professional excellence.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            <div className="text-center">
              <div className="bg-primary-600 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <Phone className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Call Us</h3>
              <p className="text-gray-300">************</p>
            </div>

            <div className="text-center">
              <div className="bg-primary-600 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <Mail className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Email Us</h3>
              <p className="text-gray-300"><EMAIL></p>
            </div>

            <div className="text-center">
              <div className="bg-primary-600 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <Clock className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Business Hours</h3>
              <p className="text-gray-300">Mon - Fri: 7am - 5pm</p>
            </div>
          </div>

          <div className="text-center">
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-primary-600 hover:bg-primary-700 text-white font-medium py-4 px-8 rounded-lg transition-colors duration-200">
                Request Free Quote
              </button>
              <button className="border border-white text-white hover:bg-white hover:text-secondary-800 font-medium py-4 px-8 rounded-lg transition-colors duration-200">
                Schedule Consultation
              </button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Services;

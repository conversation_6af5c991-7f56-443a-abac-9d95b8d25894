const ClientsSection = () => {
  const clients = [
    {
      name: 'BIC',
      logo: '/images/bic logo.svg',
      alt: 'BIC Company Logo'
    },
    {
      name: 'Fresenius Kabi',
      logo: '/images/Fresenius Kabi Logo.png',
      alt: 'Fresenius Kabi Company Logo'
    },
    {
      name: 'SPAR',
      logo: '/images/spar logo.png',
      alt: 'SPAR Company Logo'
    },
    {
      name: 'MOA',
      logo: '/images/moa Black.png',
      alt: 'MOA Company Logo'
    },
    {
      name: 'Chery',
      logo: '/images/chery logo.png',
      alt: 'Chery Company Logo'
    },
    {
      name: 'Truvelo',
      logo: '/images/Truvelo.png.webp',
      alt: 'Truvelo Company Logo'
    },
    {
      name: 'Exxaro',
      logo: '/images/EXX.JO_BIG.png',
      alt: 'Exxaro Company Logo'
    },
    {
      name: 'Lotto',
      logo: '/images/LOTTO-CMYK1.webp',
      alt: 'Lotto Company Logo'
    }
  ];

  return (
    <section className="section-padding bg-white border-b border-gray-100">
      <div className="container-custom">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="heading-lg text-slate-900 mb-4">
            Companies We've
            <span className="text-orange-600"> Worked With</span>
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Trusted by leading companies across various industries to deliver exceptional construction and building services.
          </p>
        </div>

        {/* Client Logos Carousel */}
        <div className="logo-carousel-container">
          <div className="logo-carousel-track">
            {/* First set of logos */}
            {clients.map((client, index) => (
              <div
                key={`first-${index}`}
                className="logo-carousel-item group"
              >
                <img
                  src={client.logo}
                  alt={client.alt}
                  loading="lazy"
                />
              </div>
            ))}
            {/* Duplicate set for seamless loop */}
            {clients.map((client, index) => (
              <div
                key={`second-${index}`}
                className="logo-carousel-item group"
              >
                <img
                  src={client.logo}
                  alt={client.alt}
                  loading="lazy"
                />
              </div>
            ))}
          </div>
        </div>

        {/* Optional decorative element */}
        <div className="mt-12 flex justify-center">
          <div className="w-32 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent"></div>
        </div>
      </div>
    </section>
  );
};

export default ClientsSection;

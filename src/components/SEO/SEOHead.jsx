import { useEffect } from 'react';

const SEOHead = ({
  title = "Arebone Building Enterprise - Professional Construction Services South Africa",
  description = "Arebone Building Enterprise offers professional construction, renovation, maintenance, and specialized building services across South Africa. Belgotex certified installer with Plascon certification pending.",
  keywords = "construction, building, renovation, maintenance, flooring, painting, South Africa, Belgotex, Plascon, commercial construction, residential construction",
  image = "/og-image.jpg",
  url = "https://arebone.co.za",
  type = "website"
}) => {
  useEffect(() => {
    // Update document title
    document.title = title;

    // Function to update or create meta tag
    const updateMetaTag = (name, content, property = false) => {
      const selector = property ? `meta[property="${name}"]` : `meta[name="${name}"]`;
      let meta = document.querySelector(selector);
      if (!meta) {
        meta = document.createElement('meta');
        if (property) {
          meta.setAttribute('property', name);
        } else {
          meta.setAttribute('name', name);
        }
        document.head.appendChild(meta);
      }
      meta.setAttribute('content', content);
    };

    // Update basic meta tags
    updateMetaTag('description', description);
    updateMetaTag('keywords', keywords);
    updateMetaTag('author', 'Arebone Building Enterprise');
    updateMetaTag('robots', 'index, follow');
    updateMetaTag('viewport', 'width=device-width, initial-scale=1.0');

    // Update Open Graph meta tags
    updateMetaTag('og:title', title, true);
    updateMetaTag('og:description', description, true);
    updateMetaTag('og:image', image, true);
    updateMetaTag('og:url', url, true);
    updateMetaTag('og:type', type, true);
    updateMetaTag('og:site_name', 'Arebone Building Enterprise', true);
    updateMetaTag('og:locale', 'en_ZA', true);

    // Update Twitter Card meta tags
    updateMetaTag('twitter:card', 'summary_large_image');
    updateMetaTag('twitter:title', title);
    updateMetaTag('twitter:description', description);
    updateMetaTag('twitter:image', image);

    // Update additional meta tags
    updateMetaTag('theme-color', '#2563eb');
    updateMetaTag('msapplication-TileColor', '#2563eb');
    updateMetaTag('geo.region', 'ZA');
    updateMetaTag('geo.placename', 'South Africa');
    updateMetaTag('geo.position', '-26.2041;28.0473');
    updateMetaTag('ICBM', '-26.2041, 28.0473');

    // Update canonical link
    let canonical = document.querySelector('link[rel="canonical"]');
    if (!canonical) {
      canonical = document.createElement('link');
      canonical.setAttribute('rel', 'canonical');
      document.head.appendChild(canonical);
    }
    canonical.setAttribute('href', url);

    // Update structured data
    let structuredData = document.querySelector('script[type="application/ld+json"]');
    if (!structuredData) {
      structuredData = document.createElement('script');
      structuredData.setAttribute('type', 'application/ld+json');
      document.head.appendChild(structuredData);
    }

    structuredData.textContent = JSON.stringify({
      "@context": "https://schema.org",
      "@type": "LocalBusiness",
      "name": "Arebone Building Enterprise",
      "alternateName": "ABE",
      "description": description,
      "url": url,
      "telephone": "+***********",
      "email": "<EMAIL>",
      "address": {
        "@type": "PostalAddress",
        "addressCountry": "ZA",
        "addressRegion": "Gauteng"
      },
      "geo": {
        "@type": "GeoCoordinates",
        "latitude": "-26.2041",
        "longitude": "28.0473"
      },
      "openingHours": "Mo-Fr 07:00-17:00",
      "serviceArea": {
        "@type": "State",
        "name": "South Africa"
      },
      "areaServed": [
        "Gauteng",
        "Cape Town",
        "Durban",
        "Polokwane",
        "Kimberley",
        "Bloemfontein",
        "Nelspruit"
      ],
      "hasOfferCatalog": {
        "@type": "OfferCatalog",
        "name": "Construction Services",
        "itemListElement": [
          {
            "@type": "Offer",
            "itemOffered": {
              "@type": "Service",
              "name": "Construction & Building Work",
              "description": "Complete construction solutions from foundation to finish"
            }
          },
          {
            "@type": "Offer",
            "itemOffered": {
              "@type": "Service",
              "name": "Renovations & Refurbishment",
              "description": "Transform your space with comprehensive renovation services"
            }
          },
          {
            "@type": "Offer",
            "itemOffered": {
              "@type": "Service",
              "name": "Maintenance & Repairs",
              "description": "Professional maintenance services to keep your property in excellent condition"
            }
          },
          {
            "@type": "Offer",
            "itemOffered": {
              "@type": "Service",
              "name": "Flooring Solutions",
              "description": "Expert flooring installation as a preferred Belgotex installer"
            }
          },
          {
            "@type": "Offer",
            "itemOffered": {
              "@type": "Service",
              "name": "Painting & Finishing",
              "description": "Professional painting services using premium Plascon products (certification pending)"
            }
          }
        ]
      },
      "founder": {
        "@type": "Person",
        "name": "Dwaine Moth",
        "jobTitle": "Founder & CEO"
      },
      "foundingDate": "2012",
      "numberOfEmployees": "10-50",
      "slogan": "Let's See It and Change It",
      "logo": `${url}/logo.png`,
      "image": image,
      "sameAs": [
        url
      ]
    });
  }, [title, description, keywords, image, url, type]);

  return null; // This component doesn't render anything visible
};

export default SEOHead;

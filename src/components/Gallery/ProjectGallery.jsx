import { useState } from 'react';
import { X, ChevronLeft, ChevronRight } from 'lucide-react';

const ProjectGallery = () => {
  const [selectedImage, setSelectedImage] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState('all');

  const categories = [
    { id: 'all', name: 'All Projects' },
    { id: 'construction', name: 'Construction' },
    { id: 'renovation', name: 'Renovations' },
    { id: 'flooring', name: 'Flooring' },
    { id: 'painting', name: 'Painting' },
    { id: 'maintenance', name: 'Maintenance' }
  ];

  const projects = [
    {
      id: 1,
      title: 'Office Building Construction',
      category: 'construction',
      image: '/api/placeholder/400/300',
      description: 'Complete office building construction for corporate client'
    },
    {
      id: 2,
      title: 'Retail Space Renovation',
      category: 'renovation',
      image: '/api/placeholder/400/300',
      description: 'Modern retail space transformation with contemporary design'
    },
    {
      id: 3,
      title: 'Commercial Flooring Installation',
      category: 'flooring',
      image: '/api/placeholder/400/300',
      description: 'Premium carpet installation in corporate headquarters'
    },
    {
      id: 4,
      title: 'Exterior Building Painting',
      category: 'painting',
      image: '/api/placeholder/400/300',
      description: 'Professional exterior coating using Plascon premium products'
    },
    {
      id: 5,
      title: 'Healthcare Facility Maintenance',
      category: 'maintenance',
      image: '/api/placeholder/400/300',
      description: 'Comprehensive maintenance services for Life Healthcare facility'
    },
    {
      id: 6,
      title: 'Kitchen Renovation Project',
      category: 'renovation',
      image: '/api/placeholder/400/300',
      description: 'Complete kitchen revamp with modern fixtures and finishes'
    },
    {
      id: 7,
      title: 'Warehouse Construction',
      category: 'construction',
      image: '/api/placeholder/400/300',
      description: 'Industrial warehouse construction with specialized requirements'
    },
    {
      id: 8,
      title: 'Laminate Flooring Installation',
      category: 'flooring',
      image: '/api/placeholder/400/300',
      description: 'High-quality laminate flooring for residential project'
    },
    {
      id: 9,
      title: 'Interior Office Painting',
      category: 'painting',
      image: '/api/placeholder/400/300',
      description: 'Professional interior painting with custom color scheme'
    }
  ];

  const filteredProjects = selectedCategory === 'all' 
    ? projects 
    : projects.filter(project => project.category === selectedCategory);

  const openLightbox = (project) => {
    setSelectedImage(project);
  };

  const closeLightbox = () => {
    setSelectedImage(null);
  };

  const navigateImage = (direction) => {
    const currentIndex = filteredProjects.findIndex(p => p.id === selectedImage.id);
    let newIndex;
    
    if (direction === 'next') {
      newIndex = (currentIndex + 1) % filteredProjects.length;
    } else {
      newIndex = currentIndex === 0 ? filteredProjects.length - 1 : currentIndex - 1;
    }
    
    setSelectedImage(filteredProjects[newIndex]);
  };

  return (
    <section className="section-padding bg-white">
      <div className="container-custom">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="heading-lg text-secondary-900 mb-4">
            Our Project Gallery
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Explore our portfolio of completed projects showcasing quality workmanship 
            and professional excellence across various construction services.
          </p>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`px-6 py-3 rounded-lg font-medium transition-colors duration-200 ${
                selectedCategory === category.id
                  ? 'bg-primary-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {category.name}
            </button>
          ))}
        </div>

        {/* Projects Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredProjects.map((project) => (
            <div
              key={project.id}
              className="group cursor-pointer"
              onClick={() => openLightbox(project)}
            >
              <div className="relative overflow-hidden rounded-xl shadow-lg">
                <img
                  src={project.image}
                  alt={project.title}
                  className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                />
                {/* Overlay */}
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-300 flex items-center justify-center">
                  <div className="text-white text-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 p-4">
                    <h3 className="text-lg font-semibold mb-2">{project.title}</h3>
                    <p className="text-sm">{project.description}</p>
                  </div>
                </div>
                {/* Category Badge */}
                <div className="absolute top-4 left-4 bg-primary-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                  {categories.find(cat => cat.id === project.category)?.name}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Lightbox */}
        {selectedImage && (
          <div className="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center p-4">
            <div className="relative max-w-4xl max-h-full">
              {/* Close Button */}
              <button
                onClick={closeLightbox}
                className="absolute top-4 right-4 text-white hover:text-gray-300 z-10"
              >
                <X className="h-8 w-8" />
              </button>

              {/* Navigation Buttons */}
              <button
                onClick={() => navigateImage('prev')}
                className="absolute left-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300 z-10"
              >
                <ChevronLeft className="h-8 w-8" />
              </button>
              <button
                onClick={() => navigateImage('next')}
                className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300 z-10"
              >
                <ChevronRight className="h-8 w-8" />
              </button>

              {/* Image */}
              <img
                src={selectedImage.image}
                alt={selectedImage.title}
                className="max-w-full max-h-full object-contain"
              />

              {/* Image Info */}
              <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-6 text-white">
                <h3 className="text-xl font-semibold mb-2">{selectedImage.title}</h3>
                <p className="text-gray-300">{selectedImage.description}</p>
              </div>
            </div>
          </div>
        )}

        {/* CTA Section */}
        <div className="text-center mt-16">
          <div className="bg-gray-50 rounded-2xl p-8">
            <h3 className="text-2xl font-bold text-secondary-900 mb-4">
              Ready to Start Your Project?
            </h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              Let us bring your vision to life with the same quality and attention to detail 
              showcased in our portfolio. Contact us today for a free consultation.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="btn-primary">
                Request Free Quote
              </button>
              <button className="btn-secondary">
                Schedule Consultation
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ProjectGallery;

import { Building2, MapPin } from 'lucide-react';

const ClientsSection = () => {
  const clients = [
    {
      name: 'B<PERSON>',
      logo: '/images/bic logo.svg',
      alt: 'BIC Company Logo'
    },
    {
      name: 'Fresenius Kabi',
      logo: '/images/Fresenius Kabi Logo.png',
      alt: 'Fresenius Kabi Company Logo'
    },
    {
      name: 'SPAR',
      logo: '/images/spar logo.png',
      alt: 'SPAR Company Logo'
    },
    {
      name: 'M<PERSON>',
      logo: '/images/moa Black.png',
      alt: 'MOA Company Logo'
    },
    {
      name: 'Chery',
      logo: '/images/chery logo.png',
      alt: 'Chery Company Logo'
    },
    {
      name: 'Truvelo',
      logo: '/images/Truvelo.png.webp',
      alt: 'Truvelo Company Logo'
    },
    {
      name: 'Exxaro',
      logo: '/images/EXX.JO_BIG.png',
      alt: 'Exxaro Company Logo'
    },
    {
      name: 'Lotto',
      logo: '/images/LOTTO-CMYK1.webp',
      alt: 'Lotto Company Logo'
    }
  ];

  const locations = [
    'Gauteng',
    'Cape Town',
    'Durban',
    'Polokwane',
    'Kimberley',
    'Bloemfontein',
    'Nelspruit'
  ];

  return (
    <section className="section-padding bg-gray-50">
      <div className="container-custom">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="heading-lg text-secondary-900 mb-4">
            Trusted by Leading Organizations
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            We're proud to serve some of South Africa's most respected companies and organizations, 
            delivering excellence across multiple industries and locations.
          </p>
        </div>

        {/* Client Logos Carousel */}
        <div className="mb-16">
          <h3 className="text-2xl font-semibold text-secondary-900 text-center mb-8">
            Our Valued Clients
          </h3>
          <div className="logo-carousel-container">
            <div className="logo-carousel-track">
              {/* First set of logos */}
              {clients.map((client, index) => (
                <div
                  key={`first-${index}`}
                  className="logo-carousel-item group"
                >
                  <img
                    src={client.logo}
                    alt={client.alt}
                    loading="lazy"
                  />
                </div>
              ))}
              {/* Duplicate set for seamless loop */}
              {clients.map((client, index) => (
                <div
                  key={`second-${index}`}
                  className="logo-carousel-item group"
                >
                  <img
                    src={client.logo}
                    alt={client.alt}
                    loading="lazy"
                  />
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Service Areas */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Locations */}
          <div className="space-y-6">
            <div className="flex items-center space-x-3 mb-6">
              <div className="bg-primary-100 rounded-lg p-3">
                <MapPin className="h-6 w-6 text-primary-600" />
              </div>
              <div>
                <h3 className="text-2xl font-semibold text-secondary-900">
                  National Coverage
                </h3>
                <p className="text-gray-600">Professional construction services based in Gauteng, covering South Africa</p>
              </div>
            </div>
            
            <p className="text-gray-600 leading-relaxed mb-6">
              Our experienced teams are based in Gauteng with the capability to deliver
              professional construction services across South Africa wherever our clients need us most.
            </p>

            <div className="grid grid-cols-2 gap-4">
              {locations.map((location, index) => (
                <div
                  key={index}
                  className="flex items-center space-x-3 p-4 bg-white rounded-lg border border-gray-200"
                >
                  <div className="w-2 h-2 bg-primary-600 rounded-full"></div>
                  <span className="font-medium text-secondary-900">{location}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Certifications & Partnerships */}
          <div className="space-y-6">
            <div className="flex items-center space-x-3 mb-6">
              <div className="bg-primary-100 rounded-lg p-3">
                <Building2 className="h-6 w-6 text-primary-600" />
              </div>
              <div>
                <h3 className="text-2xl font-semibold text-secondary-900">
                  Certifications & Partnerships
                </h3>
                <p className="text-gray-600">Industry-recognized standards</p>
              </div>
            </div>

            <div className="space-y-4">
              <div className="bg-white rounded-xl p-6 border border-gray-200">
                <div className="flex items-center space-x-4">
                  <img
                    src="https://belgotex.co.nz/assets/Uploads/Logo/belgotex-logo-white-1.png"
                    alt="Belgotex Certification Logo"
                    className="w-12 h-12 object-contain filter invert"
                  />
                  <div>
                    <h4 className="font-semibold text-secondary-900">
                      Belgotex Preferred Installer
                    </h4>
                    <p className="text-sm text-gray-600">
                      Certified for professional flooring installation services
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-xl p-6 border border-gray-200">
                <div className="flex items-center space-x-4">
                  <img
                    src="https://plascon.co.za/wp-content/uploads/2022/02/logo.png"
                    alt="Plascon Paint Company Logo"
                    className="w-12 h-12 object-contain"
                  />
                  <div>
                    <h4 className="font-semibold text-secondary-900">
                      Plascon Approved Applicator (Pending)
                    </h4>
                    <p className="text-sm text-gray-600">
                      Application in progress for premium paint certification
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-xl p-6 border border-gray-200">
                <div className="flex items-center space-x-4">
                  <img
                    src="https://growthpoint.co.za/wp-content/uploads/2023/12/GrowthPoint-Logo-1.svg"
                    alt="Growthpoint Property Point Partnership Logo"
                    className="w-12 h-12 object-contain"
                  />
                  <div>
                    <h4 className="font-semibold text-secondary-900">
                      Property Point Partnership
                    </h4>
                    <p className="text-sm text-gray-600">
                      Two-year incubation program participant
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center mt-16">
          <div className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">
              Join Our Growing List of Satisfied Clients
            </h3>
            <p className="text-primary-100 mb-6 max-w-2xl mx-auto">
              Experience the difference that professional service, quality workmanship, 
              and commitment to excellence can make for your next project.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-white text-primary-600 hover:bg-gray-50 font-medium py-3 px-8 rounded-lg transition-colors duration-200">
                Request Consultation
              </button>
              <button className="border border-white text-white hover:bg-white hover:text-primary-600 font-medium py-3 px-8 rounded-lg transition-colors duration-200">
                View Our Portfolio
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ClientsSection;

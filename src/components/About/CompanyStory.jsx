import { User, Calendar, MapPin, Award } from 'lucide-react';

const CompanyStory = () => {
  const milestones = [
    {
      year: '2012',
      title: 'Company Founded',
      description: '<PERSON><PERSON><PERSON> established Arebone Building Enterprise with a vision for youth development.'
    },
    {
      year: '2018',
      title: 'Belgotex Partnership',
      description: 'Became a preferred Belgotex flooring installer, expanding our service capabilities.'
    },
    {
      year: '2020',
      title: 'Property Point Program',
      description: 'Selected for a two-year incubation program with Property Point, a Growthpoint initiative.'
    },
    {
      year: '2023',
      title: 'Plascon Application',
      description: 'Applied for Plascon Paint approved applicator status to ensure quality finishes.'
    }
  ];

  return (
    <section className="section-padding bg-white">
      <div className="container-custom">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          {/* Founder Story */}
          <div className="space-y-8">
            <div className="space-y-4">
              <h2 className="heading-lg text-secondary-900">
                Our Story & Leadership
              </h2>
              <p className="text-lg text-gray-600 leading-relaxed">
                Founded by construction industry professional <PERSON><PERSON><PERSON>, Arebone Building Enterprise represents
                more than just construction – it's a commitment to building South Africa's future.
              </p>
            </div>

            {/* Founder Profile */}
            <div className="bg-gray-50 rounded-xl p-8">
              <div className="flex items-start space-x-6">
                <div className="flex-shrink-0">
                  <img
                    src="/images/dwaine-moth-profile.jpg"
                    alt="Dwaine Moth - Founder and CEO of Arebone Building Enterprise"
                    className="w-24 h-24 rounded-full object-cover"
                  />
                </div>
                <div className="space-y-3">
                  <div>
                    <h3 className="text-xl font-semibold text-secondary-900">Dwaine Moth</h3>
                    <p className="text-primary-600 font-medium">Founder & CEO</p>
                  </div>
                  <p className="text-gray-600 leading-relaxed">
                    An experienced construction industry professional with over 12 years of expertise.
                    After working for several companies, Dwaine founded Arebone in 2012 with
                    a mission to address the skills shortage in South Africa's construction sector.
                  </p>
                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                    <div className="flex items-center space-x-1">
                      <Calendar className="h-4 w-4" />
                      <span>12+ Years Experience</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Award className="h-4 w-4" />
                      <span>Industry Professional</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Company Philosophy */}
            <div className="space-y-4">
              <h3 className="text-xl font-semibold text-secondary-900">Our Philosophy</h3>
              <div className="bg-primary-50 border-l-4 border-primary-600 p-6 rounded-r-lg">
                <p className="text-gray-700 italic text-lg leading-relaxed">
                  "Our plan is to not just give a man a 'fish' to feed him for a day, 
                  but rather, teach a man to 'fish' and feed him for a lifetime."
                </p>
              </div>
              <p className="text-gray-600 leading-relaxed">
                This philosophy drives everything we do – from training young people in construction 
                skills to delivering exceptional service to our clients. We believe in creating 
                lasting impact through education, employment, and excellence.
              </p>
            </div>
          </div>

          {/* Company Timeline */}
          <div className="space-y-8">
            <div className="text-center lg:text-left">
              <h3 className="text-2xl font-semibold text-secondary-900 mb-4">
                Our Journey
              </h3>
              <p className="text-gray-600">
                Key milestones in our growth and development as a leading construction enterprise.
              </p>
            </div>

            {/* Timeline */}
            <div className="relative">
              {/* Timeline Line */}
              <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-primary-200"></div>
              
              <div className="space-y-8">
                {milestones.map((milestone, index) => (
                  <div key={index} className="relative flex items-start space-x-6">
                    {/* Timeline Dot */}
                    <div className="flex-shrink-0 w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                      {milestone.year}
                    </div>
                    
                    {/* Content */}
                    <div className="flex-grow bg-white rounded-xl p-6 shadow-sm border border-gray-100">
                      <h4 className="font-semibold text-secondary-900 mb-2">
                        {milestone.title}
                      </h4>
                      <p className="text-gray-600 text-sm leading-relaxed">
                        {milestone.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Stats */}
            <div className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-xl p-8 text-white">
              <h4 className="text-lg font-semibold mb-6 text-center">Our Impact</h4>
              <div className="grid grid-cols-2 gap-6">
                <div className="text-center">
                  <div className="text-3xl font-bold mb-1">500+</div>
                  <div className="text-primary-100 text-sm">Projects Completed</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold mb-1">50+</div>
                  <div className="text-primary-100 text-sm">Youth Trained</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold mb-1">7</div>
                  <div className="text-primary-100 text-sm">Cities Served</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold mb-1">100%</div>
                  <div className="text-primary-100 text-sm">Client Satisfaction</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CompanyStory;
